import { NextRequest, NextResponse } from 'next/server';

// Types matching MCP
interface AmazonProduct {
  asin: string;
  title: string;
  imageUrl?: string;
  price?: string;
  rating?: number;
  reviewCount?: number;
  affiliateUrl: string;
  category?: string;
}

// Mock product data for specific keywords
const getMockProducts = (keywords: string): AmazonProduct[] => {
  const mockProducts: Record<string, AmazonProduct[]> = {
    "skincare moisturizer": [
      {
        asin: "B08N6P2B8L",
        title: "The Ordinary Hyaluronic Acid 2% + B5",
        imageUrl: "https://m.media-amazon.com/images/I/61t5uGjLp+L._SL1000_.jpg",
        price: "$7.20",
        rating: 4.3,
        reviewCount: 567,
        affiliateUrl: "https://www.amazon.com/dp/B08N6P2B8L?tag=amzleanmed-20",
        category: "Beauty"
      },
      {
        asin: "B07MSR74LX",
        title: "CeraVe Moisturizing Cream",
        imageUrl: "https://m.media-amazon.com/images/I/61j6Yj3cKwL._SL1500_.jpg",
        price: "$18.99",
        rating: 4.8,
        reviewCount: 2856,
        affiliateUrl: "https://www.amazon.com/dp/B07MSR74LX?tag=amzleanmed-20",
        category: "Beauty"
      }
    ],
    "hair conditioner curl cream": [
      {
        asin: "B07GML6BG7",
        title: "Moroccanoil Extra Volume Shampoo and Conditioner",
        imageUrl: "https://m.media-amazon.com/images/I/71jQ8EhGQvL._SL1500_.jpg",
        price: "$32.00",
        rating: 4.5,
        reviewCount: 1234,
        affiliateUrl: "https://www.amazon.com/dp/B07GML6BG7?tag=amzleanmed-20",
        category: "Beauty"
      },
      {
        asin: "B08J9XLBVQ",
        title: "SheaMoisture Raw Shea Butter Leave-In Conditioner",
        imageUrl: "https://m.media-amazon.com/images/I/71AXm-ORSCL._SL1500_.jpg",
        price: "$9.99",
        rating: 4.4,
        reviewCount: 892,
        affiliateUrl: "https://www.amazon.com/dp/B08J9XLBVQ?tag=amzleanmed-20",
        category: "Beauty"
      }
    ],
    "cleanser": [
      {
        asin: "B07B9G8LGZ",
        title: "Paula's Choice CLEAR Ultra-Light Daily Hydrating Fluid Cleanser",
        imageUrl: "https://m.media-amazon.com/images/I/71ZJxVK+S0L._SL1500_.jpg",
        price: "$14.50",
        rating: 4.6,
        reviewCount: 1456,
        affiliateUrl: "https://www.amazon.com/dp/B07B9G8LGZ?tag=amzleanmed-20",
        category: "Beauty"
      }
    ],
    "sunscreen": [
      {
        asin: "B07KYTV7RQ",
        title: "La Roche-Posay Anthelios Mineral Ultra-Light Fluid SPF 50",
        imageUrl: "https://m.media-amazon.com/images/I/712JKQo2NL._SL1500_.jpg",
        price: "$22.99",
        rating: 4.4,
        reviewCount: 1567,
        affiliateUrl: "https://www.amazon.com/dp/B07KYTV7RQ?tag=amzleanmed-20",
        category: "Beauty"
      }
    ]
  };

  // Find most relevant products for keywords
  for (const [key, products] of Object.entries(mockProducts)) {
    if (keywords.toLowerCase().includes(key.split(' ')[0])) {
      return products;
    }
  }

  // Default products
  return [
    {
      asin: "B07GML6BG7",
      title: "Moroccanoil Extra Volume Shampoo",
      imageUrl: "https://m.media-amazon.com/images/I/71jQ8EhGQvL._SL1500_.jpg",
      price: "$32.00",
      rating: 4.5,
      reviewCount: 1234,
      affiliateUrl: "https://www.amazon.com/dp/B07GML6BG7?tag=amzleanmed-20",
      category: "Beauty"
    }
  ];
};

// Extract product categories from recommendations
function extractProductCategories(recommendations: string): Record<string, string> {
  const categories: Record<string, string> = {};
  const lowerText = recommendations.toLowerCase();

  const categoryMappings: Record<string, { keywords: string[], searchTerm: string }> = {
    "Skincare": {
      keywords: ["skincare", "cleanser", "moisturizer", "sunscreen", "cream", "serum", "hydration"],
      searchTerm: "skincare moisturizer"
    },
    "Hair Care": {
      keywords: ["hair", "curl", "conditioner", "shampoo", "leave-in", "treatment"],
      searchTerm: "hair conditioner curl cream"
    },
    "Grooming": {
      keywords: ["eyebrow", "beard", "grooming", "trimming", "styling", "oil"],
      searchTerm: "beard oil grooming kit"
    },
    "Face Care": {
      keywords: ["face", "facial", "pore", "texture", "tone"],
      searchTerm: "skincare moisturizer"
    }
  };

  for (const [category, config] of Object.entries(categoryMappings)) {
    const found = config.keywords.some(keyword => lowerText.includes(keyword));
    if (found) {
      categories[category] = config.searchTerm;
    }
  }

  return categories;
}

export async function POST(request: NextRequest) {
  try {
    const { recommendations } = await request.json();

    if (!recommendations || typeof recommendations !== 'string') {
      return NextResponse.json(
        { error: 'Recommendations text is required' },
        { status: 400 }
      );
    }

    // Extract categories from recommendations
    const categories = extractProductCategories(recommendations);

    // Get products for each category
    let allProducts: AmazonProduct[] = [];

    for (const [category, keywords] of Object.entries(categories)) {
      const products = getMockProducts(keywords);
      allProducts = allProducts.concat(products);
    }

    // If no categories matched, get default products
    if (allProducts.length === 0) {
      allProducts = getMockProducts("skincare moisturizer");
    }

    // Return max 3 products
    const recommendedProducts = allProducts.slice(0, 3);

    return NextResponse.json({
      products: recommendedProducts,
      totalFound: allProducts.length,
      categoriesFound: Object.keys(categories)
    });

  } catch (error) {
    console.error('Beauty recommendations error:', error);
    return NextResponse.json(
      { error: 'Failed to generate product recommendations' },
      { status: 500 }
    );
  }
}
